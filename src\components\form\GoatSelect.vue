<template>
  <el-select
    ref="selectRef"
    v-model="options.model"
    :placeholder="options.placeholder === undefined ? globalMap.lbPleaseSelect : options.placeholder"
    :size="options.size === undefined ? 'small' : options.size"
    :clearable="options.clearable === undefined ? true : options.clearable"
    :multiple="options.multiple === undefined ? false : options.multiple"
    :collapse-tags="options.multiple === undefined ? false : options.multiple"
    :collapse-tags-tooltip="options.multiple === undefined ? false : options.multiple"
    :filterable="options.filterable === undefined ? true : options.filterable"
    :allow-create="options.allowCreate === undefined ? false : options.allowCreate"
    :disabled="options.disabled === undefined ? false : options.disabled"
    :style="{ width: options.width === undefined ? '100%' : options.width }"
    :loading="options.loading === undefined ? false : options.loading"
    @change="change"
    @visible-change="visibleChange"
  >
    <template #prefix v-if="options.prefixIcon !== undefined">
      <span>
        <goat-icon :options="{ name: options.prefixIcon, color: 'var(--el-color-primary)' }" />
      </span>
    </template>
    <div v-if="options.multiple && !options.hideMultiple" style="padding-left: 20px;">
      <el-checkbox v-model="optionsAll" @change="(val) => handleOptionsAllChange(val, options)">
        全部
      </el-checkbox>
    </div>
    <el-option :key="item?.value || item?.label || index" v-for="(item, index) in displayItems" :label="labelOption(item)"  :value="item?.value" :disabled="item?.disabled || false">
      <template v-if="options.viewValue">
        <span v-if="options.icon" style="float: left; color: var(--el-color-primary); font-size: 13px">
          <goat-icon :options="{ name: item?.value || '', color: 'var(--el-color-primary)' }" />
        </span>
        <goat-tooltip v-else :options="{ text: item?.value || '' }">
          <template v-slot:tooltip-content>
            <span class="options__text">{{ item?.value || '' }}</span>
          </template>
        </goat-tooltip>
        <!-- <span v-else class="options__text">{{item?.value}}</span> -->
        <goat-tooltip :options="{ text: item?.text || '' }">
          <template v-slot:tooltip-content>
            <span class="options__value">{{ item?.text || '' }}</span>
          </template>
        </goat-tooltip>
      </template>
    </el-option>
  </el-select>
</template>

<script>
/* options
{
  model : ,
  *viewValue: true | false
  *placeholder : '',
  *size : 'large | small'
  *clearable : true | false
  *multiple  : true | false
  *filterable : true | false
  disabled
items : [
    { 
      text : '', 
      value : '', 
      icon : '',
      *disabled : true | false,
    }
  ]
}
*/

import GoatIcon from '/@components/basic/GoatIcon.vue'
import { ref, inject, computed, watch } from "vue";

export default {
  name: 'GoatSelect',
  components: {
    GoatIcon
  },
  props: ['options'],
  setup(props, context) {
    const globalNls = inject('globalNls');
    const globalMap = globalNls.getGlobalMap()

    // 创建 el-select 的 ref
    const selectRef = ref(null)

    // 创建响应式的当前值
    const currentValue = ref(props.options.model)

    // 监听 options.model 的变化
    watch(
      () => selectRef.value?.modelValue ?? props.options.model,
      (newValue) => {
        if(newValue)
          currentValue.value = newValue
      },
      { immediate: true }
    )

    const labelOption = (item)=>{
      if(!item) return '' // 防止 item 为 undefined 或 null
      if(item.showLable) return item.showLable // 自定义showlable
      return (item.value || '') + (item.value && item.text ? '/' : '') + (item.text || '')
    }

    const currentItems = computed(() => {
      let items = [];
      try {
        if (Array.isArray(props.options.items)) {
          items = props.options.items;
        } else if (props.options.items && typeof props.options.items === 'object') {
          // 处理 computed 对象或 ref 对象
          items = props.options.items.value || [];
        } else {
          items = [];
        }
      } catch (error) {
        console.warn('Error accessing options.items:', error);
        items = [];
      }
      return items;
    })

    // 优化DOM渲染：禁用时减少选项，启用时显示完整选项
    const displayItems = computed(() => {
      const isDisabled = props.options.disabled === undefined ? false : props.options.disabled;
      let items = currentItems.value;

      console.log("🚀 ~ setup ~ items:", items)

      if (isDisabled) {
        // 禁用时只保留当前选中的选项，减少DOM
        const value = currentValue.value;

        if (value !== undefined && value !== null && value !== '') {

          // 处理多选情况
          if (props.options.multiple && Array.isArray(value)) {
            return items.filter(item => value.includes(item?.value));
          }
          // 处理单选情况 - 添加类型转换匹配
          let selectedItem = items.find(item => item?.value === value);

          // 如果直接匹配失败，尝试类型转换匹配
          if (!selectedItem) {
            selectedItem = items.find(item => String(item?.value) === String(value));
          }

          if (selectedItem) {
            return [selectedItem];
          }
        }

        return []; // 没有数据时返回空数组
      }

      // 启用时返回完整选项列表
      return items;
    });

    const change = (value) => {
            if (props.options.visibleChangeType == 'Form' && props.options.visibleSelectChange !== undefined) {
        props.options.visibleSelectChange(value, props.options.formOptions, props.options.formModel, props.options.tableName, props.options.columnName);
      } else if (props.options.change !== undefined) {
        props.options.change(value);
      }
    }

    const visibleChange = (open) => {
      if (props.options.visibleChangeType !== undefined && props.options.visibleChangeType == 'Form' && props.options.visibleChange !== undefined) {
        props.options.visibleChange(open, props.options.formOptions, props.options.formModel, props.options.tableName, props.options.columnName);
      } else if (props.options.visibleChange !== undefined) {
        props.options.visibleChange(open);
      }
    }

    let optionsAll = ref(false)

    let handleOptionsAllChange = (isAll, options) => {
      console.log('isAll', options);
      if(options.multiple) {
        if (isAll) {
          let items = currentItems.value;
          options.formModel[options.columnName] = items.map(item => {
            return item?.value
          }).filter(value => value !== undefined && value !== null)
        } else {
          options.formModel[options.columnName] = []
        }
      }
    }
    return {
      globalMap,
      selectRef,
      labelOption,
      change,
      visibleChange,
      optionsAll,
      handleOptionsAllChange,
      displayItems
    }
  }

}
</script>

<style>
</style>