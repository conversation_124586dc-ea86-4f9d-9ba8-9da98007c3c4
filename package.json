{"name": "vite", "version": "0.0.0", "scripts": {"serve": "vite preview", "build": "vite build", "dev": "vite", "force": "vite --force", "preview": "vite preview --port 8001"}, "dependencies": {"@bpmn-io/properties-panel": "^0.13.2", "@bundled-es-modules/axios": "^0.21.1", "@howdyjs/mouse-menu": "^2.0.5", "@quasar/extras": "^1.12.2", "@vitejs/plugin-vue-jsx": "^1.3.10", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^0.26.0", "bpmn-auto-layout": "^0.1.0", "bpmn-js": "^9.2.2", "bpmn-js-color-picker": "^0.4.0", "bpmn-js-properties-panel": "^1.1.1", "bpmn-js-task-resize": "^1.2.0", "camunda-bpmn-moddle": "^6.1.2", "clipboard": "^2.0.10", "codemirror": "^5.59.2", "crypto-browserify": "^3.12.0", "crypto-js": "^4.2.0", "diagram-js-minimap": "^2.1.1", "drawflow": "^0.0.58", "echarts": "^5.4.3", "el-table-draggable": "^1.4.4", "element-plus": "2.3.14", "file-saver": "^2.0.5", "gridstack": "^5.0.0", "html-to-image": "^1.11.11", "lodash": "^4.17.21", "mitt": "^3.0.0", "moment": "^2.29.4", "pinia": "^2.0.14", "pinia-plugin-persistedstate": "^1.5.1", "poros": "2.0.8-hx", "print-js": "^1.6.0", "quasar": "^2.4.2", "roboto-fontface": "*", "socket.io-client": "^4.4.1", "sockjs-client": "^1.6.0", "sortablejs": "^1.14.0", "splitpanes": "^3.1.1", "sprintf-js": "^1.1.2", "typescript": "^4.5.5", "util": "^0.10.0", "vue": "^3.2.25", "vue-bpmn": "^0.3.0", "vue-draggable-next": "^2.1.1", "vue-pdf-embed": "^1.1.6", "vue-router": "^4.0.12", "vue3-clipboard": "^1.0.0", "vue3-cookies": "^1.0.6", "vue3-json-excel": "^1.0.9-alpha", "vue3-json-viewer": "^2.2.2", "vue3-pdfjs": "^0.1.6", "vue3-xlsx": "^1.1.1", "vuedraggable": "^2.24.3", "vuetify": "^3.0.0-alpha.0", "vuex": "^4.0.2", "webfontloader": "^1.0.0", "webstomp-client": "^1.2.6", "xlsx": "^0.18.4"}, "devDependencies": {"@iconify-json/academicons": "^1.1.3", "@iconify-json/bi": "^1.1.6", "@iconify-json/bx": "^1.1.2", "@iconify-json/carbon": "^1.1.4", "@iconify-json/cil": "^1.1.2", "@iconify-json/clarity": "^1.1.4", "@iconify-json/dashicons": "^1.1.2", "@iconify-json/el": "^1.1.1", "@iconify-json/fa-solid": "^1.1.2", "@iconify-json/fluent": "^1.1.15", "@iconify-json/fluent-mdl2": "^1.1.0", "@iconify-json/gg": "^1.1.2", "@iconify-json/gridicons": "^1.1.2", "@iconify-json/ic": "^1.1.4", "@iconify-json/iconoir": "^1.1.6", "@iconify-json/ion": "^1.1.1", "@iconify-json/material-symbols": "^1.1.2", "@iconify-json/mdi": "^1.1.13", "@iconify-json/ri": "^1.1.1", "@iconify/vue": "^3.2.1", "@mdi/font": "^5.9.55", "@quasar/vite-plugin": "^1.0.3", "@types/drawflow": "^0.0.4", "@vitejs/plugin-vue": "^2.3.3", "@vue/babel-plugin-jsx": "^1.1.1", "@vue/cli-plugin-babel": "5.0.0-beta.7", "@vue/cli-service": "5.0.0-beta.7", "@vuetify/vite-plugin": "^1.0.0-alpha.3", "material-design-icons-iconfont": "^6.1.1", "sass": "^1.32.0", "sass-loader": "^10.0.0", "unplugin-auto-import": "^0.5.5", "unplugin-icons": "^0.14.3", "unplugin-vue-components": "^0.17.21", "vite": "^2.7.2", "vite-plugin-proxy": "^0.5.0", "vue-cli-plugin-vuetify": "~2.4.5", "vuetify-loader": "^2.0.0-alpha.0"}}