<template>
  <goat-content :options="{ height: windowSize.contentHeight }">
    <template v-slot:content>
      <el-row>
        <el-col>
          <el-card>
            <div style="display: flex">
              <goat-form class="theme-search-form" ref="searchForm" :options="searchFormOptions" />
              <!-- <div style="margin-top: 4px; margin-right: 6px; font-size: 12px; font-weight: bold">工艺卡</div>
              <el-tree-select
                style="width: 200px"
                ref="treeRef"
                v-model="selectTree"
                value-key="label"
                node-key="label"
                :props="defaultProps"
                :data="data"
                :filter-node-method="filterNode"
                @node-click="handleClick"
                highlight-current
                default-expand-all
              >
                <template #default="{ data }">
                  <span class="custom-tree-node">
                    <span :style="optionStyleColor(data)">{{ data.label }} </span>
                  </span>
                </template>
</el-tree-select> -->
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row class="card-none-border-padding">
        <splitpanes class="default-theme" :dbl-click-splitter="false" :push-other-panes="false"
          @resized="tabItem.setTableLayoutChange">
          <pane :size="85">
            <el-card>
              <goat-header :options="leftTableIconOptions" />
              <goat-view-table ref="leftTableRef" :options="leftTableOptions" @rowClick="leftTableRowClick" />
            </el-card>
          </pane>
        </splitpanes>
      </el-row>
    </template>
  </goat-content>
  <commonAddEdit ref="commonAddEdit" :confirmBack="confirmBack"></commonAddEdit>

  <commonPublishCard ref="commonPublishCard" :confirmBack="confirmBack"></commonPublishCard>

  <upload-dialog ref="uploadDialogRef" @uploadSuccess="uploadSuccess"></upload-dialog>
</template>

<script>

import { reactive, ref, inject, onMounted, watch, computed, nextTick, toRaw } from "vue";
import otherCard from "./otherProcessCard/modules/otherCard.vue";
import commonAddEdit from "./components/commonAddEdit.vue";
import commonPublishCard from "./components/commonPublishCard.vue";
import uploadDialog from "./components/uploadDialog.vue";
import { ElMessageBox } from 'element-plus'
import {
  getTableHeightByOneLineSearch
} from "/@utils/height.js";

export default {
  name: "CreateConsumable",
  components: { otherCard, commonAddEdit, commonPublishCard, uploadDialog },
  props: ["options", "tabItem", "writeAble"],
  inject: ["windowSize"],
  setup(props, context) {
    let nlsMap = reactive({
      lbFactoryName: '工厂',
      lbProcessFlowType: '工艺路线类型',
      lbDetailProcessFlowType: '工艺类型明细',
      lbProcessFlowName: '工艺路线',
      lbActive: '激活',
      lbArchive: '存档',
      lbFrozen: '冻结',
      lbDescription: '描述',
      lbFilterKeyword: '过滤字段',
      lbRevision: '版本',
      btAdd: '新增',
      btRefresh: '刷新',
      btOpen: '打开',
      btOk: '确认',
      btExit: '退出',
    })
    const useAxiosPost = inject("useAxiosPost");
    const deepCopy = inject('deepCopy')

    const messageShow = inject("messageShow");
    const messageConfirm = inject("messageConfirm");
    const messageAlert = inject("messageAlert");
    const getUser = inject("getUser");
    const getButtonPermission = inject("getButtonPermission");
    const factoryName = getUser().FACTORYNAME;

    const globalNls = inject('globalNls');
    const globalMap = globalNls.getGlobalMap()


    const loginUser = inject("loginUser");
    const user = loginUser.getUser();
    let selectTree = ref()
    let commonAddEdit = ref()
    let commonPublishCard = ref()
    let uploadDialogRef = ref()


    let CardRef = ref();
    let searchFormRef = ref();
    let treeRef = ref(null);
    let leftCurrentRow = ref(null);
    let pushNIOList = ref([
      {
        value: 1,
        text: '是'
      },
      {
        value: 0,
        text: '否'
      },
    ])
    // let getButtonPermission = (menuKey) => {
    //   if(!menuKey) return
    //   let hasPermission = false
    //   const flag = location.hostname == 'localhost'
    //   if(!flag) {
    //     // 格创平台按钮权限
    //     let buttonMaks = localStorage.getItem('buttonMaks')
    //     buttonMaks = JSON.parse(buttonMaks)
    //     hasPermission = buttonMaks.includes(menuKey)
    //   } else {
    //     hasPermission = true
    //   }
    //   return hasPermission
    // }

    const getCardType = (type) => {
      let cardType = ''
      switch (type) {
        //     搅拌工艺卡:detailProcessOperationType='Mixing'
        // 烘烤工艺卡:detailProcessOperationType='Oven'
        // 其他:detailProcessOperationType=null */
        case 'Mixing': // 搅拌工艺卡
          cardType = 'stirProcessCard'
          break;
        // case 'Oven':  // 烘烤工艺卡
        //   cardType = 'bakeProcessCard'
        //   break;
        // case 'Formation': // 化成 分容
        //   cardType = 'partialVolumeCard'
        //   break;
        default:
          cardType = 'otherProcessCard'
          break;
      }
      return cardType
    }
    const uploadSuccess = () => {
      searchMain() // 刷新
    }

    const confirmBack = (type, cardInfo) => {
      searchMain() // 刷新
      leftCurrentRow.value = undefined;
      leftTableCheckList.value = []
      let { processOperationName, productSpecName, machineName, cardName, cardRevision, cardTypeDetail, recipeName, cardState } = cardInfo
      // processOperationName = processOperationName ? processOperationName.join(',') : ''
      if (type == 'upgrade') cardState = 'PreReleased'
      let cardType = getCardType(cardTypeDetail)
      context.emit("openMenu", cardType, { processOperationName, productSpecName, machineName, cardName, cardRevision, recipeName, cardState });
    }

    const leftTableRowClick = (row, column, event) => {
      leftCurrentRow.value = row;
      currentCardInfo.value = row
    };

    let leftTableRef = ref();
    let leftTableData = ref([]);
    let leftColumnList = ref([]);
    let leftTableCheckList = ref([]);
    let leftHeight = getTableHeightByOneLineSearch(55);
    let leftViewId = ref('OperationCardList_List');
    let leftTableOptions = reactive({
      model: "",
      excelName: "POList",
      viewId: leftViewId,
      rowKey: "po",
      height: leftHeight,
      writeAble: props.writeAble,
      isIndex: false,
      isSelection: false,
      isPager: true,
      isExport: true,
      columnList: leftColumnList,
      rowList: leftTableData,
      rowSelectList: leftTableCheckList,
      nlsMap: nlsMap,
    });

    let data = ref([]) // tree
    let searchForm = ref();
    const defaultProps = {
      children: 'children',
      label: 'label',
    }
    let searchFormModel = reactive({
      cardName: null, // 工艺卡编码
      productOrderName: null, // 工单
      // processOperationName: 'GF-C010A', // 工序
      // productSpecName: 'B.PI.C.001', // 成品料号
      processOperationName: '', // 工序
      productSpecName: '', // 成品料号
      machineName: null, // 设备
      cardState: 'Released',
      pushNIO: '',
    })
    let productOrderNameList = ref([]);  // 工单
    let processOperationList = ref([]);  // 工序
    let productSpecList = ref([]); // 产品
    let machineSpecList = ref([]); // 设备
    let specModelList = ref([])
    let cardStateList = ref([])
    let currentSelectState = ref('') // 当前选中工艺卡的状态，用于禁止修改判断
    let currentCardInfo = ref('')


    let leftTableIconOptions = reactive({
      title: "工艺卡",
      buttons: [
        {
          options: {
            priorityView: props.writeAble,
            writeAble: props.writeAble,
            size: 'small',
            iconDirection: 'left',
            round: true,
            isIcon: true,
            icon: { name: 'IconPromotion' },
            isTooltip: true,
            text: computed(() => '推送蔚来'),
            tooltip: { text: computed(() => '推送蔚来') },
            click: () => {
              const onSuccess = (data) => {
                messageAlert(data.message || '推送成功', 'success')
              }
              const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }
              let body = {
              }
              let param = {
                userId: user.USERID
              }
              useAxiosPost('/NioController/controlField', body, param, onSuccess, onFail, props)
            },
          },
        },
        {
          options: {
            writeAble: props.writeAble,
            size: 'small',
            iconDirection: 'left',
            round: true,
            isIcon: true,
            icon: { name: 'IconCirclePlus' },
            isTooltip: true,
            text: computed(() => nlsMap.btAdd),
            tooltip: { text: computed(() => nlsMap.btAdd) },
            // disabled: computed(() => leftCurrentRow.value && leftCurrentRow.value.cardState == 'Released'),
            click: () => {
              commonAddEdit.value && commonAddEdit.value.openModel('add', {
                processOperationList: processOperationList.value,
                productSpecList: productSpecList.value,
                machineSpecList: []
              })
            },
          },
        },
        {
          options: {
            writeAble: props.writeAble,
            size: 'small',
            iconDirection: 'left',
            round: true,
            isIcon: true,
            icon: { name: 'IconSwitchButton' },
            isTooltip: true,
            text: computed(() => globalMap.btCopy),
            tooltip: { text: computed(() => globalMap.btCopy) },
            disabled: computed(() => !leftCurrentRow.value),
            click: () => {
              let params = deepCopy(currentCardInfo.value)
              let dict = {
                processOperationList: processOperationList.value,
                productSpecList: productSpecList.value,
                machineSpecList: machineSpecList.value,
              }
              commonAddEdit.value && commonAddEdit.value.openModel('copy', dict, params)
            },
          },
        },
        {
          options: {
            writeAble: props.writeAble,
            size: 'small',
            iconDirection: 'left',
            round: true,
            isIcon: true,
            icon: { name: 'IconEdit', },
            isTooltip: true,
            text: '修改',
            tooltip: { text: '修改' },
            //  只有发布状态可以修改
            disabled: computed(() => !leftCurrentRow.value || leftCurrentRow.value.cardState == 'Released'),
            click: () => {
              console.log('cardInfocardInfo', currentCardInfo.value);
              let params = deepCopy(currentCardInfo.value)
              let dict = {
                processOperationList: processOperationList.value,
                productSpecList: productSpecList.value,
                machineSpecList: machineSpecList.value,
              }
              commonAddEdit.value && commonAddEdit.value.openModel('update', dict, params)
            },
          },
        },
        {
          options: {
            writeAble: props.writeAble,
            size: 'small',
            iconDirection: 'left',
            round: true,
            isIcon: true,
            icon: { name: 'IconDelete' },
            isTooltip: true,
            text: '删除',
            tooltip: { text: '删除' },
            //  只有发布状态可以删除
            disabled: computed(() => !leftCurrentRow.value || leftCurrentRow.value.cardState == 'Released'),
            click: () => {
              console.log('currentCardInfo', currentCardInfo.value);
              const onSuccess = (data) => {
                console.log('data删除data删除data删除', data);
                // 判断是否有删除权限
                let auth = getButtonPermission('delProcessCard')
                if (!auth) {
                  messageAlert('你没有删除工艺卡权限，不允许删除')
                  return
                }
                messageConfirm(`确认删除改工艺卡吗?`, () => {
                  const onSuccess = (resdata) => {
                    messageShow('delete')
                    nextTick(() => {
                      selectTree.value = ''
                      searchMain() // 刷新tree
                    })
                  }
                  const onFail = (resdata) => { messageAlert(resdata.message || resdata.msg, 'warning') }
                  let body = {
                    cardName: currentCardInfo.value.cardName,
                    cardRevision: currentCardInfo.value.cardRevision,
                  }
                  let param = null
                  useAxiosPost('/OperationCardController/DeleteOperationCard', body, param, onSuccess, onFail, props)
                }, 'warning')

              }
              const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }
              let body = {
                cardName: currentCardInfo.value.cardName,
                cardRevision: currentCardInfo.value.cardRevision,
              }
              let param = null
              useAxiosPost('/OperationCardApi/checkIsUsed', body, param, onSuccess, onFail, props)


            },
          },
        },
        {
          options: {
            writeAble: props.writeAble,
            size: 'small',
            iconDirection: 'left',
            round: true,
            isIcon: true,
            icon: { name: 'IconSwitchButton' },
            isTooltip: true,
            // text: '附件上传',
            // tooltip: { text: computed(() => '附件上传') },
            text: computed(() => globalMap.btattachmentUpload),
            tooltip: { text: computed(() => globalMap.btattachmentUpload) },
            disabled: computed(() => !leftCurrentRow.value),
            click: () => {
              let params = deepCopy(leftCurrentRow.value)
              uploadDialogRef.value && uploadDialogRef.value.openModel(params)
            },
          },
        },
        {
          options: {
            writeAble: props.writeAble,
            size: 'small',
            iconDirection: 'left',
            round: true,
            isIcon: true,
            icon: { name: 'IconDownload' },
            isTooltip: true,
            text: '升级',
            tooltip: { text: '升级' },
            disabled: computed(() => !leftCurrentRow.value || leftCurrentRow.value.cardState != 'Released'),
            click: () => {
              //  先校验
              // A.允许修改工艺卡版本号状态为“发布”的工艺卡版本号
              // B.允许修改工艺卡版本号状态为“禁用”的工艺卡版本号
              // 弹窗
              let params = deepCopy(currentCardInfo.value)
              let dict = {
                processOperationList: processOperationList.value,
                productSpecList: productSpecList.value,
                machineSpecList: machineSpecList.value,
              }
              commonAddEdit.value && commonAddEdit.value.openModel('upgrade', dict, params)
            },
          },
        },
        {
          options: {
            writeAble: props.writeAble,
            size: 'small',
            iconDirection: 'left',
            round: true,
            isIcon: true,
            icon: { name: 'IconSwitchButton' },
            isTooltip: true,
            text: '启动',
            tooltip: { text: '启动' },
            disabled: computed(() => !leftCurrentRow.value || leftCurrentRow.value.cardState != 'Released'),
            click: () => {
              messageConfirm(`是否启动?`, () => {
                const onSuccess = (resdata) => {
                  messageShow('update')
                  nextTick(() => {
                    searchMain()
                  })
                }
                const onFail = (resdata) => { messageAlert(resdata.message || resdata.msg, 'warning') }
                let body = {
                  ...leftCurrentRow.value
                }
                let param = null
                useAxiosPost('/OperationCardController/activeCard', body, param, onSuccess, onFail, props)
              }, 'warning')

            },
          },
        },
        // {
        //   options: {
        //     writeAble: props.writeAble,
        //     size: 'small',
        //     iconDirection: 'left',
        //     round: true,
        //     isIcon: true,
        //     icon: { name: 'IconSwitchButton' },
        //     isTooltip: true,
        //     text: '发布',
        //     tooltip: { text: '发布' },
        //     disabled: computed(() => !leftCurrentRow.value),
        //     click: () => {
        //       let params = deepCopy(currentCardInfo.value)
        //       const continuePublished = (content) => {
        //         ElMessageBox.confirm(
        //           `${content}`,
        //           '发布工艺卡',
        //           {
        //             confirmButtonText: '继续发布',
        //             cancelButtonText: '取消',
        //             type: 'warning',
        //             draggable: true,
        //           }
        //         ).then(() => {
        //           commonPublishCard.value && commonPublishCard.value.openModel(params, true) // 点的继续发布传true
        //         }).catch(() => { })
        //       }
        //       // messageConfirm(`确定生产工单${leftCurrentRow.value.wo}发布吗？`, () => {
        //       //   updateState('Shipped')
        //       // }, 'warning')
        //       // 校验
        //       const onSuccess = (data) => {
        //         if (data.message) { // messageg 校验C 继续发布
        //           continuePublished(data.message)
        //         } else { // 没有则是直接通过
        //           commonPublishCard.value && commonPublishCard.value.openModel(params, false)
        //         }
        //       }
        //       const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }  // 校验A B
        //       let body = {
        //         cardName: currentCardInfo.value.cardName,
        //         cardRevision: currentCardInfo.value.cardRevision,
        //       }
        //       let param = null
        //       useAxiosPost('/OperationCardController/PublishOperationCardProcessor', body, param, onSuccess, onFail, props)
        //     },
        //   },
        // },
        {
          options: {
            writeAble: props.writeAble,
            size: 'small',
            iconDirection: 'left',
            round: true,
            isIcon: true,
            icon: { name: 'IconDocumentAdd' },
            isTooltip: true,
            text: '前往工艺卡管理',
            tooltip: { text: '前往工艺卡管理' },
            disabled: computed(() => !leftCurrentRow.value),
            click: () => {
              let { processOperationName, productSpecName, machineName, cardName, cardRevision, cardTypeDetail, recipeName, cardState } = leftCurrentRow.value
              let cardType = getCardType(cardTypeDetail)
              context.emit("tabRemove", {
                name: cardType
              }, true);
              context.emit("openMenu", cardType, { processOperationName, productSpecName, machineName, cardName, cardRevision, recipeName, cardState });
            },
          },
        },
      ],
    });
    const searchFormOptions = reactive({
      ref: searchFormRef,
      model: searchFormModel,
      inline: true,
      labelPosition: "left",
      labelWidth: "",
      size: "",
      items: [
        {
          prop: 'cardName',
          name: 'GoatInput',
          label: '工艺卡编码',
          labelWidth: '',
          required: false,
          options: {
            width: '200px',
            model: searchFormModel.cardName,
            disabled: false
          },
        },
        // {
        //   prop: "productOrderName",
        //   label: '工单号',
        //   name: "GoatSelect",
        //   labelWidth: "",
        //   required: false,
        //   options: {
        //     viewValue: true,
        //     width: '200px',
        //     model: searchFormModel.productOrderName,
        //     items: productOrderNameList,
        //     change: (value) => {
        //       let findItem = {}
        //       searchFormModel.productSpecName = undefined
        //       if (searchFormModel.productOrderName) {
        //         findItem = productOrderNameList.value.find(item => item.value === searchFormModel.productOrderName)
        //       }
        //       if (findItem && findItem.productSpecName) {
        //         initProductSpecList(findItem.productSpecName)
        //       } else {
        //         initProductSpecList()
        //       }
        //     },
        //   },
        // },
        {
          prop: "processOperationName",
          label: "工序号",
          name: "GoatSelect",
          labelWidth: "",
          required: false,
          options: {
            viewValue: true,
            width: '200px',
            model: searchFormModel.processOperationName,
            items: processOperationList,
            change: (changeValue) => {
              searchFormModel.machineName = undefined
              if (changeValue) {
                data.value = []
                selectTree.value = null
                initMachineSpecData(changeValue)
              } else {
                data.value = []
                selectTree.value = null
                initMachineSpecData()
              }
            },
          },
        },
        // {
        //   prop: "productSpecName",
        //   label: "产品名称",
        //   name: "GoatSelect",
        //   labelWidth: "",
        //   required: false,
        //   options: {
        //     viewValue: true,
        //     width: '200px',
        //     model: searchFormModel.productSpecName,
        //     items: productSpecList,
        //     change: (changeValue) => {
        //       data.value = []
        //       selectTree.value = null
        //     },
        //   },
        // },
        {
          prop: "machineName",
          label: "设备号",
          name: "GoatSelect",
          labelWidth: "",
          required: false,
          options: {
            viewValue: true,
            width: '200px',
            model: searchFormModel.machineName,
            items: machineSpecList,
            change: (changeValue) => {
              data.value = []
              selectTree.value = null
            },
          },
        },
        {
          prop: "specModel",
          label: "产品型号",
          name: "GoatSelect",
          labelWidth: "",
          required: false,
          options: {
            viewValue: true,
            width: '200px',
            model: searchFormModel.specModel,
            items: specModelList
          },
        },
        {
          prop: "cardState",
          label: "工艺卡状态",
          name: "GoatSelect",
          labelWidth: "",
          required: false,
          options: {
            viewValue: true,
            width: '200px',
            model: searchFormModel.cardState,
            items: cardStateList
          },
        },
        {
          name: 'GoatSelect',
          label: '是否推送蔚来',
          labelWidth: '',
          prop: 'pushNIO',
          options: {
            viewValue: true,
            model: searchFormModel.pushNIO,
            items: pushNIOList
          },
        },
        {
          name: "GoatButton",
          labelWidth: "",
          label: "",
          prop: "",
          rules: [],
          options: {
            onlyIcon: false,
            isIcon: true,
            icon: { name: "IconSearch", },
            iconDirection: "left",
            isTooltip: true,
            text: '查询',
            tooltip: { text: '查询', },
            click: () => {
              leftCurrentRow.value = undefined;
              currentCardInfo.value = undefined
              searchMain(1);
            },
          },
          next: [
            {
              name: "GoatButton",
              label: "",
              prop: "",
              rules: [],
              options: {
                onlyIcon: false,
                isIcon: true,
                icon: { name: "IconRefresh", },
                iconDirection: "left",
                isTooltip: true,
                text: computed(() => nlsMap.btRefresh),
                tooltip: { text: computed(() => nlsMap.btRefresh), },
                click: () => {
                  searchFormModel.productOrderName = null
                  searchFormModel.processOperationName = null
                  searchFormModel.productSpecName = null
                  searchFormModel.machineName = null
                  leftCurrentRow.value = undefined;
                  currentCardInfo.value = undefined
                  searchMain(1);
                },
              },
            },
          ],
        },
      ],
    });


    const optionStyleColor = computed(() => (item) => {
      let styleObj = {}
      let config = {
        'Released': '#0bb582', // 发布
        'PreReleased': '#fdde55', // 待发布
        'NoRelease': '#999', // 禁止
        // 'Del': '#999', // 删除
      }
      styleObj.color = config[item.cardState]
      return styleObj
    })

    const filterNode = (value, data) => {
      if (!value) return true
      return data.label.includes(value)
    }
    const filterText = ref('')
    watch(filterText, (val) => {
      treeRef.value && treeRef.value.filter(val)
    })

    let disableEdit = computed(() => {
      if (!selectTree.value) {
        return true
      }
      //A.	允许修改待发布  B.	允许修改工艺卡版本号状态为“禁用”且未被使用过
      // let flag = !['PreReleased', 'NoRelease'].includes(currentSelectState.value)
      // console.log('aaaaa', currentSelectState.value)
      return false
    })
    const handleClick = (itemdata, node) => {
      console.log('itemdata', itemdata, selectTree);
      if (node.level < 2) {
        return
      }
      const paramData = toRaw(itemdata)
      currentSelectState.value = itemdata.cardState
    }

    const searchMain = (page) => {
      const onSuccess = (data) => {
        if (data.datas.length > 0) {
          leftTableData.value = data.datas;
        } else {
          leftTableData.value = []
        }
      }
      const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }

      let body = {
        token_factory: factoryName,
        ...searchFormModel
      }
      let param = { viewId: leftViewId.value }
      useAxiosPost('/QueryDefApi/queryByViewId', body, param, onSuccess, onFail, props)
    }

    const search = () => {
      const onSuccess = (res) => {
        if (res.datas.length > 0) {
          let result = []
          res.datas.forEach(item => {
            Object.keys(item).forEach(ele => {
              let parentObj = {
                label: ele, // 'PI-PE-2023-OP00-0001 key值'
                children: item[ele].map(v => ({
                  label: v.cardRevision,
                  cardState: v.cardState,
                  cardName: v.cardName,
                  children: []
                }))
              }
              result.push(parentObj)
            })
          });
          data.value = result
        }
      }
      //   { "PI-PE-2023-OP00-0001":[{},]}
      const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }
      let { processOperationName, productSpecName, machineName } = searchFormModel
      let body = {
        processOperationName: processOperationName,
        productSpecName: productSpecName,
        machineName: machineName
      }
      let param = {}
      useAxiosPost('/OperationCardApi/getOperationCardList', body, null, onSuccess, onFail)
    }

    const reset = () => {
      data.value = []
      selectTree.value = null
      searchFormModel.cardName = null
      searchFormModel.productOrderName = null
      searchFormModel.processOperationName = null
      searchFormModel.machineName = null
      searchFormModel.productSpecName = null
      CardRef.value && CardRef.value.initData({}, {})
    }

    /* 工单 */
    const initProductOrderNameList = () => {
      const onSuccess = (data) => {
        if (data.datas.length > 0) {
          productOrderNameList.value = data.datas.map(item => ({
            label: item.productOrderName,
            value: item.productOrderName,
            text: item.productOrderName,
            productSpecName: item.productSpecName
          }))
        }
      }
      const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }
      let body = {}
      let param = { queryId: 'get_productOrderName_list' }
      useAxiosPost('/QueryDefApi/query', body, param, onSuccess, onFail)
    }
    /* 工序号 */
    const initProcessOperationList = () => {
      const onSuccess = (data) => {
        if (data.datas.length > 0) {
          processOperationList.value = data.datas.map(item => ({
            label: item.description,
            value: item.processOperationName,
            text: item.description,
            // valueLabel: `${item.processOperationName}(${item.description})`
          }))
        }
      }
      const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }
      /*
      搅拌工艺卡:detailProcessOperationType='Mixing'
      烘烤工艺卡:detailProcessOperationType='Oven'
      其他:detailProcessOperationType=null */
      let body = {
        detailProcessOperationType: null,
      }
      let param = { queryId: 'Get_ProcessOperationData' }

      useAxiosPost('/QueryDefApi/query', body, param, onSuccess, onFail)
    }
    /* 产品 */
    const initProductSpecList = (value = null) => {
      const onSuccess = (data) => {
        if (data.datas.length > 0) {
          productSpecList.value = data.datas.map(item => ({
            label: item.description,
            text: item.description,
            value: item.productSpecName,
          }))
        }
      }
      const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }
      // 工单的productSpecName
      let body = { productSpecName: value }
      let param = { queryId: 'get_productSpecList' }

      useAxiosPost('/QueryDefApi/query', body, param, onSuccess, onFail)
    }
    /* 设备 */
    const initMachineSpecData = (value = null) => {
      const onSuccess = (data) => {
        if (data.datas.length > 0) {
          machineSpecList.value = data.datas.map(item => ({
            label: item.description,
            text: item.description,
            value: item.machineName,
          }))
        }
      }
      const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }

      let body = { processOperationName: value }
      let param = { queryId: 'Get_MachineSpecData' }
      useAxiosPost('/QueryDefApi/query', body, param, onSuccess, onFail)
    }
    let initSpecModelList = () => {
      const onSuccess = (data) => {
        if (data.datas.length > 0) {
          specModelList.value = data.datas.map(item => ({
            label: item.specModel,
            text: item.specModel,
            value: item.specModel,
          }))
        }
      }
      const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }

      let body = {}
      let param = { queryId: 'GetSpecModel' }
      useAxiosPost('/QueryDefApi/query', body, param, onSuccess, onFail)
    }
    let initCardStateList = () => {
      const onSuccess = (data) => {
        if (data.datas.length > 0) {
          cardStateList.value = data.datas.map(item => {
            let value = item.value.split('OperationCardState_')[1]
            return {
              label: item.text,
              text: item.text,
              value: value,
            }
          })
          cardStateList.value.forEach(item => {
            nlsMap[item.value] = item.text
          })
        }
      }
      const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }

      let body = { enumname: "OperationCardState" }
      let param = { queryId: 'Common_C_GetEnumValue' }
      useAxiosPost('/QueryDefApi/query', body, param, onSuccess, onFail)
    }

    /* 混料方式 */
    const initCardMixingMode = (value = null) => {
      const onSuccess = (data) => {
        if (data.datas.length > 0) {
          // dicts.mixingModeList = data.datas.map(item => ({
          //   showLable: item.text,
          //   text: item.text,
          //   value: item.value,
          // }))
          data.datas.forEach(item => {
            nlsMap[item.value] = item.text
          })
        }
      }
      const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }
      let body = {
        enumname: 'OperationCardMixingMode',
      }
      let param = { queryId: 'Common_C_GetEnumValue' }
      useAxiosPost('/QueryDefApi/query', body, param, onSuccess, onFail)
    }
    onMounted(() => {
      initProductOrderNameList()
      initProcessOperationList()
      initProductSpecList()
      initMachineSpecData()
      initSpecModelList()
      initCardStateList()
      initCardMixingMode()
      searchMain()
    });


    return {
      uploadSuccess,
      globalMap,
      uploadDialogRef,

      selectTree,
      commonAddEdit,
      commonPublishCard,

      CardRef,
      searchFormRef,
      treeRef,
      filterText,
      defaultProps,
      filterNode,
      data,
      handleClick,
      optionStyleColor,

      searchFormOptions,
      leftTableIconOptions,

      disableEdit,
      productOrderNameList,
      processOperationList,
      productSpecList,
      machineSpecList,
      searchFormModel,
      search,
      reset,
      confirmBack,
      leftTableOptions,
      leftTableRowClick,
      leftTableRef,
      leftHeight
    };
  },
};
</script>

<style scoped lang="scss">
.el-card {
  overflow-y: scroll;
}

::-webkit-scrollbar {
  width: 0px;
}

// ::v-deep .el-input__inner {
//   height: 18px !important;
// }
.button_wrap {
  .item_btn {
    width: 20%;
    margin-right: 4%;
    margin-left: 0%;
    margin-bottom: 10px;

    &:nth-child(4n) {
      margin-right: 0;
    }
  }
}

.img {
  width: 100%;
  height: 70px;
  background: url('../../assets/logo_luotuo.png') center center no-repeat;
}

.table-wrap {
  flex: 1;
  display: flex;
  flex-direction: column;

  .table-column-th {
    display: inline-flex;
    align-items: center;
    width: 100%;

    .itemColumns {
      // flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }
  }

  .table-content {}
}

.itemdataClass {
  display: flex;
  font-size: 20px;
  // font-weight: bold;
  // height: 50px;
  width: 100%;

  .li {
    display: flex;
    // flex: 1;
    height: 100%;
    align-items: center;
    justify-content: center;
  }
}

.el-icon svg {
  width: 20px;
  height: 20px;
}
</style>