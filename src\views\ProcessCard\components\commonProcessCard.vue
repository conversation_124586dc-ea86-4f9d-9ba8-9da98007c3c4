<template>
  <goat-content :options="{ height: windowSize.contentHeight }">
    <template v-slot:content>
      <el-row>
        <el-col>
          <el-card>
            <div style="display: flex">
              <goat-form class="theme-search-form" ref="searchForm" :options="searchFormOptions" />
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row class="card-none-border-padding">
        <splitpanes class="default-theme" :dbl-click-splitter="false" :push-other-panes="false" @resized="tabItem.setTableLayoutChange">
          <pane :size="85">
            <el-card>
              <goat-header :options="leftTableIconOptions" />
              <el-card :style="{ height: windowSize.contentHeight - 95 + 'px' }">
                <el-scrollbar :height="windowSize.contentHeight - 113">
                  <OtherCard v-if="cardType == 'OtherCard'" :previewData="searchFormModel" :hiddenBtn="hiddenBtn" ref="CardRef"></OtherCard>
                  <StirCard v-if="cardType == 'StirCard'" :previewData="searchFormModel" :hiddenBtn="hiddenBtn" ref="CardRef"></StirCard>
                  <BakeCard v-if="cardType == 'BakeCard'" :previewData="searchFormModel" :hiddenBtn="hiddenBtn" ref="CardRef"></BakeCard>
                  <PartialCard v-if="cardType == 'PartialCard'" :previewData="searchFormModel" :hiddenBtn="hiddenBtn" ref="CardRef"></PartialCard>
                </el-scrollbar>
              </el-card>
            </el-card>
          </pane>
        </splitpanes>
      </el-row>
    </template>
  </goat-content>
  <el-dialog :destroy-on-close="true" width="1400px" v-model="dialogVisible" title="预览" :before-close="handleClose">
    <div class="previewDialog2">
      <!-- 只有在对话框显示时才渲染组件，避免重复创建 -->
      <OtherCard v-if="dialogVisible && cardType == 'OtherCard'" :previewData="searchFormModel" :hiddenBtn="true" ref="CardDialogRef"></OtherCard>
      <StirCard v-if="dialogVisible && cardType == 'StirCard'" :previewData="searchFormModel" :hiddenBtn="true" ref="CardDialogRef"></StirCard>
      <BakeCard v-if="dialogVisible && cardType == 'BakeCard'" :previewData="searchFormModel" :hiddenBtn="true" ref="CardDialogRef"></BakeCard>
      <PartialCard v-if="dialogVisible && cardType == 'PartialCard'" :previewData="searchFormModel" :hiddenBtn="true" ref="CardDialogRef"></PartialCard>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="mr6" @click="dialogVisible = false">取消</el-button>
        <el-button class="mr6" type="primary" @click="print"> 打印 </el-button>
        <!-- <el-button type="primary" @click="dialogVisible = false"> 下载 </el-button> -->
      </span>
    </template>
  </el-dialog>
  <goat-dialog :options="drawerOptions">
    <template v-slot:content>
      <div>
        <div style="text-align: center; font-size: 24px">{{ drawerOptionsTip }}</div>
        <goat-form-row ref="rightFormRef" :options="rightFormOptions" />
      </div>
    </template>
  </goat-dialog>
  <CommonPublishCard ref="commonPublishCard" :confirmBack="getOperationCard"></CommonPublishCard>
</template>

<script>

import { reactive, ref, inject, onMounted, watch, computed, nextTick, watchEffect } from "vue";
import OtherCard from "../otherProcessCard/modules/otherCard.vue";
import StirCard from "../stirProcessCard/modules/stirCard.vue";
import BakeCard from "../bakeProcessCard/modules/bakeCard.vue";
import PartialCard from "../partialVolumeCard/modules/PartialCard.vue";
import CommonPublishCard from "./commonPublishCard.vue";
import { ElMessageBox } from 'element-plus'
import printJS from 'print-js'
import * as htmlToImage from 'html-to-image';
import { useUserStore } from '/@stores/userStore.js'
import moment from 'moment'

export default {
  name: "CreateConsumable",
  components: { OtherCard, StirCard, BakeCard, PartialCard, CommonPublishCard },
  props: ["options", "tabItem", "writeAble", 'cardType'],
  inject: ["windowSize"],
  setup(props, context) {
    let nlsMap = reactive({
      lbFactoryName: '工厂',
      lbProcessFlowType: '工艺路线类型',
      lbDetailProcessFlowType: '工艺类型明细',
      lbProcessFlowName: '工艺路线',
      lbActive: '激活',
      lbArchive: '存档',
      lbFrozen: '冻结',
      lbDescription: '描述',
      lbFilterKeyword: '过滤字段',
      lbRevision: '版本',
      btAdd: '新增',
      btRefresh: '刷新',
      btOpen: '打开',
      btOk: '确认',
      btExit: '退出',
    })
    const useAxiosPost = inject("useAxiosPost");
    const useJsonCopy = inject("useJsonCopy");
    const isEmpty = inject('isEmpty')
    const deepCopy = inject('deepCopy')

    const messageShow = inject("messageShow");
    const messageConfirm = inject("messageConfirm");
    const messageAlert = inject("messageAlert");
    const getButtonPermission = inject("getButtonPermission");
    let cardType = ref('')

    watchEffect(() => {
      cardType.value = props.cardType
    })

    let selectTree = ref()
    let commonAddEdit = ref()
    let commonPublishCard = ref()

    let CardRef = ref();
    let CardDialogRef = ref();
    let searchFormRef = ref();
    let treeRef = ref(null);
    let dialogVisible = ref(false)

    let previewData = ref()
    let page = ref()
    let cardInfo = ref({
      cardName: '',
      cardRevision: ''
    })
    let hiddenBtn = ref(false)

    let data = ref([]) // tree
    let drawerOptionsTip = ref('请确认是否提交以上工艺卡')
    const defaultProps = {
      children: 'children',
      label: 'label',
    }
    let searchFormModel = reactive({
      productOrderName: null, // 工单
      // processOperationName: 'GF-C010A', // 工序
      // productSpecName: 'B.PI.C.001', // 成品料号
      processOperationName: '', // 工序
      productSpecName: '', // 成品料号
      machineName: null // 设备
    })
    let productOrderNameList = ref([]);  // 工单
    let processOperationList = ref([]);  // 工序
    let productSpecList = ref([]); // 产品
    let machineSpecList = ref([]); // 设备

    let currentSelectState = ref('') // 当前选中工艺卡的状态，用于禁止修改判断
    let currentCardInfo = ref('')
    // let getButtonPermission = (menuKey) => {
    //   if(!menuKey) return
    //   let hasPermission = false
    //   const flag = location.hostname == 'localhost'
    //   if(!flag) {
    //     // 格创平台按钮权限
    //     let buttonMaks = localStorage.getItem('buttonMaks')
    //     buttonMaks = JSON.parse(buttonMaks)
    //     hasPermission = buttonMaks.includes(menuKey)
    //   } else {
    //     hasPermission = true
    //   }
    //   return hasPermission
    // }
    let leftTableIconOptions = reactive({
      title: "工艺卡",
      buttons: [
        {
          options: {
            writeAble: props.writeAble,
            size: 'small',
            iconDirection: 'left',
            round: true,
            isIcon: true,
            icon: { name: 'IconCirclePlus' },
            isTooltip: true,
            text: '保存',
            tooltip: { text: '保存' },
            disabled: computed(() => !selectTree.value || hiddenBtn.value),
            click: () => {
              nextTick(() => {
                CardRef.value && CardRef.value.onSave()
              })
            },
          },
        },
        // {
        //   options: {
        //     writeAble: props.writeAble,
        //     size: 'small',
        //     iconDirection: 'left',
        //     round: true,
        //     isIcon: true,
        //     icon: { name: 'IconEdit' },
        //     isTooltip: true,
        //     text: '发布',
        //     tooltip: { text: '发布' },
        //     disabled: computed(() => !selectTree.value || hiddenBtn.value),
        //     click: () => {
        //       let params = deepCopy(currentCardInfo.value)
        //       const continuePublished = (content) => {
        //         ElMessageBox.confirm(
        //           `${content}`,
        //           '发布工艺卡',
        //           {
        //             confirmButtonText: '继续发布',
        //             cancelButtonText: '取消',
        //             type: 'warning',
        //             draggable: true,
        //           }
        //         ).then(() => {
        //           commonPublishCard.value && commonPublishCard.value.openModel(params, true) // 点的继续发布传true
        //         }).catch(() => { })
        //       }
        //       // messageConfirm(`确定生产工单${leftCurrentRow.value.wo}发布吗？`, () => {
        //       //   updateState('Shipped')
        //       // }, 'warning')
        //       // 校验
        //       const onSuccess = (data) => {
        //         if (data.message) { // messageg 校验C 继续发布
        //           continuePublished(data.message)
        //         } else { // 没有则是直接通过
        //           commonPublishCard.value && commonPublishCard.value.openModel(params, false)
        //         }
        //       }
        //       const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }  // 校验A B
        //       let body = {
        //         cardName: currentCardInfo.value.cardName,
        //         cardRevision: currentCardInfo.value.cardRevision,
        //       }
        //       let param = null
        //       useAxiosPost('/OperationCardController/PublishOperationCardProcessor', body, param, onSuccess, onFail, props)
        //     },
        //   },
        // },
        {
          options: {
            writeAble: props.writeAble,
            size: 'small',
            iconDirection: 'left',
            round: true,
            isIcon: true,
            icon: { name: 'IconSwitchButton' },
            isTooltip: true,
            text: '预览',
            tooltip: { text: '预览' },
            disabled: computed(() => !selectTree.value),
            click: () => {
              nextTick(() => {
                getOperationCard(true)
              })
            },
          },
        },
        {
          options: {
            writeAble: props.writeAble,
            size: 'small',
            iconDirection: 'left',
            round: true,
            isIcon: true,
            icon: { name: 'IconSwitchButton' },
            isTooltip: true,
            text: '提交',
            tooltip: { text: '提交' },
            // disabled: computed(() => !selectTree.value),
            disabled: computed(() => {
              let hasPermission = getButtonPermission('subProcessCard')
              localStorage.setItem("cardCantEdit", !hasPermission || currentCardInfo.value.cardState != 'PreReleased')
              return !hasPermission || currentCardInfo.value.cardState != 'PreReleased'
            }),
            click: () => {
              let params = deepCopy(currentCardInfo.value)
              // 校验
              const onSuccess = (data) => {
                if (data.message) {
                  ElMessageBox.confirm(
                    `${data.message}`,
                    '发布工艺卡',
                    {
                      confirmButtonText: '继续发布',
                      cancelButtonText: '取消',
                      type: 'warning',
                      draggable: true,
                    }
                  ).then(() => {
                    formType.value = 'tj'
                    drawerOptions.isOpen = true
                    drawerOptions.title = '提交工艺卡'
                    drawerOptions.buttons[1].options.text = '确认提交'
                    drawerOptionsTip.value = '请确认是否提交以上工艺卡'
                    useJsonCopy(rightFormModel, rightFormInitModel)
                    rightFormOptions.items[1].label = '提交说明'
                    rightFormOptions.items[0].required = false
                  }).catch(() => { })
                } else {
                  formType.value = 'tj'
                  drawerOptions.isOpen = true
                  drawerOptions.title = '提交工艺卡'
                  drawerOptions.buttons[1].options.text = '确认提交'
                  drawerOptionsTip.value = '请确认是否提交以上工艺卡'
                  useJsonCopy(rightFormModel, rightFormInitModel)
                  rightFormOptions.items[1].label = '提交说明'
                  rightFormOptions.items[0].required = false
                }
              }
              const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }  // 校验A B
              let body = {
                cardName: currentCardInfo.value.cardName,
                cardRevision: currentCardInfo.value.cardRevision,
              }
              let param = null
              useAxiosPost('/OperationCardController/PublishOperationCardProcessor', body, param, onSuccess, onFail, props)
            },
          },
        },
        {
          options: {
            writeAble: props.writeAble,
            size: 'small',
            iconDirection: 'left',
            round: true,
            isIcon: true,
            icon: { name: 'IconSwitchButton' },
            isTooltip: true,
            text: '审核',
            tooltip: { text: '审核' },
            // disabled: computed(() => !selectTree.value),
            disabled: computed(() => {
              let hasPermission = getButtonPermission('checkProcessCard')
              return !hasPermission || currentCardInfo.value.cardState != "PreAudit"
            }),
            click: () => {
              // // 判断数据是否改变
              // let result = true
              // if(CardRef.value) {
              //   result = CardRef.value.checkData()
              // }
              // if(!result) {
              //   // 如果数据变了
              //   messageConfirm('界面数据已变更，是否直接保存并审批', () => {
              //     CardRef.value && CardRef.value.onSave()
              //   }, 'warning')
              //   return
              // }
              formType.value = 'sh'
              drawerOptions.isOpen = true
              drawerOptions.title = '审核工艺卡'
              drawerOptions.buttons[1].options.text = '确认审核'
              drawerOptionsTip.value = '请审核当前工艺卡'
              useJsonCopy(rightFormModel, rightFormInitModel)
              rightFormOptions.items[1].label = '审核说明'
              rightFormOptions.items[0].label = '审核结果'
              rightFormOptions.items[0].rules[0].message = '请选择审核结果'
              // let params = deepCopy(currentCardInfo.value)
              // // 校验
              // const onSuccess = (data) => {

              // }
              // const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }  // 校验A B
              // let body = {
              //   cardName: currentCardInfo.value.cardName,
              //   cardRevision: currentCardInfo.value.cardRevision,
              // }
              // let param = null
              // useAxiosPost('/OperationCardController/PublishOperationCardProcessor', body, param, onSuccess, onFail, props)
            },
          },
        },
        {
          options: {
            writeAble: props.writeAble,
            size: 'small',
            iconDirection: 'left',
            round: true,
            isIcon: true,
            icon: { name: 'IconSwitchButton' },
            isTooltip: true,
            text: '批准',
            tooltip: { text: '批准' },
            // disabled: computed(() => !selectTree.value),
            disabled: computed(() => {
              let hasPermission = getButtonPermission('approveProcessCard')
              return !hasPermission || currentCardInfo.value.cardState != 'PreApproval'
            }),
            click: () => {
              formType.value = 'pz'
              drawerOptions.isOpen = true
              drawerOptions.title = '批准工艺卡'
              drawerOptions.buttons[1].options.text = '确认发布'
              drawerOptionsTip.value = '请批准当前工艺卡'
              useJsonCopy(rightFormModel, rightFormInitModel)
              rightFormOptions.items[1].label = '批准说明'
              rightFormOptions.items[0].label = '批准结果'
              rightFormOptions.items[0].rules[0].message = '请选择批准结果'
              // let params = deepCopy(currentCardInfo.value)
              // // 校验
              // const onSuccess = (data) => {
              // }
              // const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }  // 校验A B
              // let body = {
              //   cardName: currentCardInfo.value.cardName,
              //   cardRevision: currentCardInfo.value.cardRevision,
              // }
              // let param = null
              // useAxiosPost('/OperationCardController/PublishOperationCardProcessor', body, param, onSuccess, onFail, props)
            },
          },
        },
      ],
    });
    const searchFormOptions = reactive({
        ref: searchFormRef,
        model: searchFormModel,
        inline: true,
        labelPosition: "left",
        labelWidth: "",
        size: "",
        items: [
        {
          prop: "productOrderName",
          label: '工单号',
          name: "GoatSelect",
          labelWidth: "",
          required: false,
          options: {
            viewValue: true,
            width: '200px',
            // 移除 model 属性，避免双重绑定
            items: productOrderNameList,
            disabled: true,
            change: (value) => {
              // 使用 nextTick 避免频繁调用
              nextTick(() => {
                let findItem = {}
                searchFormModel.productSpecName = undefined
                if (searchFormModel.productOrderName) {
                  findItem = productOrderNameList.value.find(item => item.value === searchFormModel.productOrderName)
                }
                if (findItem && findItem.productSpecName) {
                  initProductSpecList(findItem.productSpecName)
                } else {
                  initProductSpecList()
                }
              });
            },
          },
        },
        {
          prop: "processOperationName",
          label: "工序号",
          name: "GoatSelect",
          labelWidth: "",
          required: false,
          options: {
            viewValue: true,
            width: '200px',
            // 移除 model 属性，避免双重绑定
            items: processOperationList,
            disabled: true,
            change: (changeValue) => {
              nextTick(() => {
                searchFormModel.machineName = undefined
                if (changeValue) {
                  data.value = []
                  selectTree.value = null
                  initMachineSpecData(changeValue)
                } else {
                  data.value = []
                  selectTree.value = null
                  initMachineSpecData()
                }
              });
            },
          },
        },
        {
          prop: "productSpecName",
          label: "产品名称",
          name: "GoatSelect",
          labelWidth: "",
          required: false,
          options: {
            viewValue: true,
            width: '200px',
            // 移除 model 属性，避免双重绑定
            items: productSpecList,
            disabled: true,
            change: (changeValue) => {
              nextTick(() => {
                data.value = []
                selectTree.value = null
                search()
              });
            },
          },
        },
        {
          prop: "machineName",
          label: "设备号",
          name: "GoatSelect",
          labelWidth: "",
          required: false,
          options: {
            viewValue: true,
            width: '200px',
            // 移除 model 属性，避免双重绑定
            items: machineSpecList,
            disabled: true,
            change: (changeValue) => {
              nextTick(() => {
                data.value = []
                selectTree.value = null
                search()
              });
            },
          },
        },
      ],
    });
    let rightFormRef = ref()
    let formType = ref('add');  // add | update | view
    let rightFormModel = reactive({
      result: '',
      eventComment: ''
    })

    let rightFormInitModel = reactive({
      result: '',
      eventComment: ''
    })

    let drawerOptions = reactive({
      isOpen: false,
      size: '75%',
      writeAble: props.writeAble,
      tabItem: props.tabItem,
      title: '发布工艺卡',
      buttons: [
        {
          options: {
            writeAble: props.writeAble,
            size: 'small',
            iconDirection: 'left',
            round: true,
            isIcon: true,
            isTooltip: true,
            icon: { name: 'IconCloseBold' },
            text: '取消',
            tooltip: { text: '取消', },
            click: () => {
              drawerOptions.isOpen = false
            },
          },
        },
        {
          options: {
            writeAble: props.writeAble,
            size: 'small',
            iconDirection: 'left',
            round: true,
            isIcon: true,
            isTooltip: true,
            // hidden: computed(() => leftCurrentRow.value && leftCurrentRow.value.productOrderState == 'Issued'),
            icon: { name: 'IconSelect' },
            text: '确认提交',
            tooltip: { text: '确认提交', },
            click: () => {
              console.log('确认提交');

              const loginUser = useUserStore();
              let user = loginUser.getUser();
              let formRef = rightFormRef.value.formRef;
              if (!formRef) return;
              formRef.validate((valid) => {
                if (valid) {
                  let subAciton = () => {
                    // 校验
                    const onSuccess = (data) => {
                      console.log('onSuccess', data);
                      drawerOptions.isOpen = false
                      if (formType.value == 'tj') {
                        messageAlert('提交审核成功', 'success')
                      } else if (formType.value == 'sh') {
                        messageAlert('审核成功', 'success')
                      } else if (formType.value == 'pz') {
                        messageAlert('批准成功', 'success')
                      }
                    }
                    const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }  // 校验A B
                    let body = {}
                    if (formType.value == 'tj') {
                      // 提交
                      body = {
                        cardName: currentCardInfo.value.cardName,
                        cardRevision: currentCardInfo.value.cardRevision,
                        // cardName: "PI-PE-A010-2309-01", //工艺卡名称
                        // cardRevision: "A02", //工艺卡版本
                        sourceCardState: "PreReleased", //源状态
                        sourceStateDesc: "提交", //源状态描述
                        toCardState: "PreAudit", //目标状态
                        toCardStateDesc: "待审核", //目标状态描述
                        processOperationName: currentCardInfo.value.processOperationName, //工序编码
                        editor: user.USERID, //编辑者
                        auditor: "", //审批者
                        approver: "", //批准者
                        eventTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'), //事件时间
                        result: "Y", //结果
                        resultDesc: "通过", //结果描述(通过/未通过)
                        eventComment: rightFormModel.eventComment //备注
                      }
                    } else if (formType.value == 'sh') {
                      if (rightFormModel.result == 'Y') {
                        // 审核通过
                        body = {
                          cardName: currentCardInfo.value.cardName,
                          cardRevision: currentCardInfo.value.cardRevision,
                          processOperationName: currentCardInfo.value.processOperationName, //工序编码
                          sourceCardState: "PreAudit", //源状态
                          sourceStateDesc: "待审核", //源状态描述
                          toCardState: "PreApproval", //目标状态
                          toCardStateDesc: "待批准", //目标状态描述
                          editor: currentCardInfo.value.editor, //编辑者
                          auditor: user.USERID, //审批者
                          approver: "", //批准者
                          eventTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'), //事件时间
                          result: 'Y', //结果
                          resultDesc: "通过", //结果描述(通过/未通过)
                          eventComment: rightFormModel.eventComment //备注
                        }
                      } else {
                        // 审核不通过
                        body = {
                          cardName: currentCardInfo.value.cardName,
                          cardRevision: currentCardInfo.value.cardRevision,
                          processOperationName: currentCardInfo.value.processOperationName, //工序编码
                          sourceCardState: "PreAudit", //源状态
                          sourceStateDesc: "待审核", //源状态描述
                          toCardState: "PreReleased", //目标状态
                          toCardStateDesc: "提交", //目标状态描述
                          editor: currentCardInfo.value.editor, //编辑者
                          auditor: user.USERID, //审批者
                          approver: "", //批准者
                          eventTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'), //事件时间
                          result: 'N', //结果
                          resultDesc: '未通过', //结果描述(通过/未通过)
                          eventComment: rightFormModel.eventComment //备注
                        }
                      }
                    } else if (formType.value == 'pz') {
                      if (rightFormModel.result == 'Y') {
                        // 批准通过
                        body = {
                          cardName: currentCardInfo.value.cardName,
                          cardRevision: currentCardInfo.value.cardRevision,
                          processOperationName: currentCardInfo.value.processOperationName, //工序编码
                          sourceCardState: "PreApproval", //源状态
                          sourceStateDesc: "待批准", //源状态描述
                          toCardState: "Released", //目标状态
                          toCardStateDesc: "发布", //目标状态描述
                          editor: currentCardInfo.value.editor, //编辑者
                          auditor: currentCardInfo.value.auditor, //审批者
                          approver: user.USERID, //批准者
                          eventTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'), //事件时间
                          result: 'Y', //结果
                          resultDesc: "通过", //结果描述(通过/未通过)
                          eventComment: rightFormModel.eventComment //备注
                        }
                      } else {
                        // 批准不通过
                        body = {
                          cardName: currentCardInfo.value.cardName,
                          cardRevision: currentCardInfo.value.cardRevision,
                          processOperationName: currentCardInfo.value.processOperationName, //工序编码
                          sourceCardState: "PreApproval", //源状态
                          sourceStateDesc: "待批准", //源状态描述
                          toCardState: "PreReleased", //目标状态
                          toCardStateDesc: "提交", //目标状态描述
                          editor: currentCardInfo.value.editor, //编辑者
                          auditor: currentCardInfo.value.auditor, //审批者
                          approver: user.USERID, //批准者
                          eventTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'), //事件时间
                          result: 'N', //结果
                          resultDesc: '未通过', //结果描述(通过/未通过)
                          eventComment: rightFormModel.eventComment //备注
                        }
                      }
                    }
                    let param = null
                    console.log('currentCardInfo.value', currentCardInfo.value, body);
                    useAxiosPost('/OperationCardController/changeOperationCardState', body, param, onSuccess, onFail, props)
                  }
                  if (formType.value == 'sh' || formType.value == 'pz' || formType.value == 'tj') {
                    // 判断数据是否改变
                    let result = true
                    if (CardRef.value) {
                      result = CardRef.value.checkData()
                    }
                    if (!result) {

                      // 如果数据变了
                      messageConfirm('界面数据已变更，是否直接保存并审批', async () => {
                        await CardRef.value.onSave()
                        subAciton()
                      }, 'warning')
                      return
                    }
                  }
                  subAciton()
                }
              })
              // drawerOptions.isOpen = false
              // confirmNext('Created')
            },
          },
        },
      ]
    })
    let rightFormOptions = reactive({
      ref: rightFormRef,
      model: rightFormModel,
      labelWidth: '120px',
      items: [
        {
          name: 'GoatRadio',
          label: '审核结果',
          prop: 'result',
          required: true,
          rules: [
            {
              required: true,
              message: '请选择审核结果',
              trigger: 'change',
            },
          ],
          hidden: computed(() => formType.value == 'tj'),
          options: {
            width: '370px',
            // 移除 model 属性，避免双重绑定
            items: [
              {
                value: 'Y',
                text: '通过'
              },
              {
                value: 'N',
                text: '不通过'
              },
            ]
          },
        },
        {
          name: 'GoatInput',
          label: '提交说明',
          prop: 'eventComment',
          required: true,
          // hidden: computed(() => formType.value != 'add'),
          hidden: false,
          rules: [
            {
              required: true,
              message: '提交说明不能为空',
              trigger: 'change',
            },
          ],
          options: {
            // disabled: computed(() => isPoDisabled.value),
            // 移除 model 属性，避免双重绑定
          }
        },
      ],
    });
    const optionStyleColor = computed(() => (item) => {
      let styleObj = {}
      let config = {
        'Released': '#0bb582', // 发布
        'PreReleased': '#fdde55', // 待发布
        'NoRelease': '#999', // 禁止
        // 'Del': '#999', // 删除
      }
      styleObj.color = config[item.cardState]
      return styleObj
    })

    const filterNode = (value, data) => {
      if (!value) return true
      return data.label.includes(value)
    }
    const filterText = ref('')
    watch(filterText, (val) => {
      treeRef.value && treeRef.value.filter(val)
    })

    let disableEdit = computed(() => {
      if (!selectTree.value) {
        return true
      }
      //A.	允许修改待发布  B.	允许修改工艺卡版本号状态为“禁用”且未被使用过
      // let flag = !['PreReleased', 'NoRelease'].includes(currentSelectState.value)
      // console.log('aaaaa', currentSelectState.value)
      return false
    })

    // 获取工艺卡数据 preview 是否预览标识
    const getOperationCard = (preview) => {
      const onSuccess = (data) => {
        if (data.datas.length > 0) {
          previewData.value = JSON.parse(data.datas[0].previewData)
          currentCardInfo.value = deepCopy(data.datas[0])
          page.value = data.datas[0]
          // 如果预览，则打开弹窗
          if (preview) {
            dialogVisible.value = true
            nextTick(() => {
              CardDialogRef.value && CardDialogRef.value.initData(previewData.value, page.value)
            })
          } else {
            nextTick(() => {
              CardRef.value && CardRef.value.initData(previewData.value, page.value)
            })
          }
        }
      }
      const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }
      let body = {
        cardName: cardInfo.value.cardName,
        cardRevision: cardInfo.value.cardRevision,
      }
      let param = {}
      useAxiosPost('/OperationCardController/getOperationCard', body, null, onSuccess, onFail)
    }

    const search = () => {
      const onSuccess = (res) => {
        if (res.datas.length > 0) {
          let result = []
          res.datas.forEach(item => {
            Object.keys(item).forEach(ele => {
              let parentObj = {
                label: ele, // 'PI-PE-2023-OP00-0001 key值'
                children: item[ele].map(v => ({
                  label: v.cardRevision,
                  cardState: v.cardState,
                  cardName: v.cardName,
                  children: []
                }))
              }
              result.push(parentObj)
            })
          });
          data.value = result
        }
      }
      //   { "PI-PE-2023-OP00-0001":[{},]}
      const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }
      let { processOperationName, productSpecName, machineName } = searchFormModel
      let body = {
        processOperationName: processOperationName,
        productSpecName: productSpecName,
        machineName: machineName
      }
      let param = {}
      useAxiosPost('/OperationCardApi/getOperationCardList', body, null, onSuccess, onFail)
    }

    const reset = () => {
      data.value = []
      selectTree.value = null
      searchFormModel.productOrderName = null
      searchFormModel.processOperationName = null
      searchFormModel.machineName = null
      searchFormModel.productSpecName = null
      CardRef.value && CardRef.value.initData({}, {})
    }

    /* 工单 */
    const initProductOrderNameList = () => {
      const onSuccess = (data) => {
        if (data.datas.length > 0) {
          productOrderNameList.value = data.datas.map(item => ({
            label: item.productOrderName,
            value: item.productOrderName,
            text: item.productOrderName,
            productSpecName: item.productSpecName
          }))
        }
      }
      const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }
      let body = {}
      let param = { queryId: 'get_productOrderName_list' }
      useAxiosPost('/QueryDefApi/query', body, param, onSuccess, onFail)
    }
    /* 工序号 */
    const initProcessOperationList = () => {
      const onSuccess = (data) => {
        if (data.datas.length > 0) {
          processOperationList.value = data.datas.map(item => ({
            label: item.description,
            value: item.processOperationName,
            text: item.description,
            // valueLabel: `${item.processOperationName}(${item.description})`
          }))
        }
      }
      const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }
      /*
      搅拌工艺卡:detailProcessOperationType='Mixing'
      烘烤工艺卡:detailProcessOperationType='Oven'
      其他:detailProcessOperationType=null */
      let body = {
        detailProcessOperationType: null,
      }
      let param = { queryId: 'Get_ProcessOperationData' }

      useAxiosPost('/QueryDefApi/query', body, param, onSuccess, onFail)
    }
    /* 产品 */
    const initProductSpecList = (value = null) => {
      const onSuccess = (data) => {
        if (data.datas.length > 0) {
          productSpecList.value = data.datas.map(item => ({
            label: item.description,
            text: item.description,
            value: item.productSpecName,
          }))
        }
      }
      const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }
      // 工单的productSpecName
      let body = { productSpecName: value }
      let param = { queryId: 'get_productSpecList' }

      useAxiosPost('/QueryDefApi/query', body, param, onSuccess, onFail)
    }
    /* 设备 */
    const initMachineSpecData = (value = null) => {
      const onSuccess = (data) => {
        if (data.datas.length > 0) {
          machineSpecList.value = data.datas.map(item => ({
            label: item.description,
            text: item.description,
            value: item.machineName,
          }))
        }
      }
      const onFail = (data) => { messageAlert(data.message || data.msg, 'warning') }

      let body = { processOperationName: value }
      let param = { queryId: 'Get_MachineSpecData' }
      useAxiosPost('/QueryDefApi/query', body, param, onSuccess, onFail)
    }

    // 打印
    const print = () => {
      var previewDialog = document.getElementsByClassName('previewDialog2')[0];
      console.log('node', previewDialog);
      htmlToImage.toJpeg(previewDialog)
        .then(function (dataUrl) {
          console.log('dataUrl', dataUrl);
          printJS({ printable: dataUrl, type: 'image' })
          // var img = new Image();
          // img.src = dataUrl;
          // document.body.appendChild(img);
        })
        .catch(function (error) {
          console.error('oops, something went wrong!', error);
        });
      // window.print()
      // printJS({printable: image-url,type: 'image'})
    }

    watch(
      () => props.tabItem.preData,
      (value) => {
        if (value) {
          let { processOperationName, productSpecName, machineName, cardName, cardRevision, recipeName, cardState } = value
          searchFormModel.processOperationName = processOperationName
          searchFormModel.productSpecName = productSpecName
          searchFormModel.machineName = machineName
          searchFormModel.recipeName = recipeName
          if (!cardName || !cardRevision) return
          if (cardState == 'Released') hiddenBtn.value = true
          // search()
          cardInfo.value.cardName = cardName
          cardInfo.value.cardRevision = cardRevision
          getOperationCard()
          selectTree.value = cardRevision
        }
      },
      { immediate: true, deep: true }
    )

    onMounted(() => {
      initProductOrderNameList()
      initProcessOperationList()
      initProductSpecList()
      initMachineSpecData()
      // search() // for 调试
    });


    return {
      selectTree,
      commonAddEdit,
      commonPublishCard,

      CardRef,
      CardDialogRef,
      searchFormRef,
      treeRef,
      filterText,
      defaultProps,
      filterNode,
      data,
      optionStyleColor,

      searchFormOptions,
      leftTableIconOptions,

      disableEdit,
      productOrderNameList,
      processOperationList,
      productSpecList,
      machineSpecList,
      searchFormModel,
      search,
      reset,
      dialogVisible,
      print,
      cardType,
      previewData,
      hiddenBtn,
      getOperationCard,
      drawerOptions,
      rightFormOptions,
      rightFormRef,
      drawerOptionsTip
    };
  },
};
</script>

<style scoped lang="scss">
.el-card {
  overflow-y: scroll;
}
::-webkit-scrollbar {
  width: 0px;
}

// ::v-deep .el-input__inner {
//   height: 18px !important;
// }
.button_wrap {
  .item_btn {
    width: 20%;
    margin-right: 4%;
    margin-left: 0%;
    margin-bottom: 10px;
    &:nth-child(4n) {
      margin-right: 0;
    }
  }
}

.img {
  width: 100%;
  height: 70px;
  background: url('../../assets/logo_luotuo.png') center center no-repeat;
}

.table-wrap {
  flex: 1;
  display: flex;
  flex-direction: column;
  .table-column-th {
    display: inline-flex;
    align-items: center;
    width: 100%;
    .itemColumns {
      // flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }
  }
  .table-content {
  }
}

.itemdataClass {
  display: flex;
  font-size: 20px;
  // font-weight: bold;
  // height: 50px;
  width: 100%;
  .li {
    display: flex;
    // flex: 1;
    height: 100%;
    align-items: center;
    justify-content: center;
  }
}
.el-icon svg {
  width: 20px;
  height: 20px;
}
</style>